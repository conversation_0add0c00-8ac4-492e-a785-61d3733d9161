# Provider System Demo Commands

This document shows how to test the provider system manually through the CLI.

## Basic Provider Management

### 1. Show provider help
```
/provider
```

### 2. List providers (should be empty initially)
```
/provider list
```

### 3. Add a local Ollama provider
```
/provider add
```
Then enter:
- Provider ID: `local-ollama`
- Provider name: `Local Ollama`
- Base URL: `http://localhost:11434/v1`
- API Key: `dummy-key`
- Default model: `llama3.2`
- Provider type: `openai-compatible`
- Description: `Local Ollama instance`

### 4. Add an OpenAI provider
```
/provider add
```
Then enter:
- Provider ID: `my-openai`
- Provider name: `My OpenAI`
- Base URL: `https://api.openai.com/v1`
- API Key: `sk-proj-your-actual-key-here`
- Default model: `gpt-4`
- Provider type: `openai-compatible`
- Description: `Custom OpenAI configuration`

### 5. List providers again
```
/provider list
```

### 6. Show detailed provider info
```
/provider show local-ollama
/provider show my-openai
```

### 7. Test provider connections
```
/provider test local-ollama
/provider test my-openai
```

### 8. Set default provider
```
/provider set-global my-openai
```

### 9. List providers to see default marked
```
/provider list
```

## Advanced Usage

### 10. Test with different provider types

#### Groq Provider
```
/provider add
```
- Provider ID: `groq`
- Provider name: `Groq`
- Base URL: `https://api.groq.com/openai/v1`
- API Key: `gsk_your-groq-key-here`
- Default model: `llama-3.1-70b-versatile`
- Provider type: `openai-compatible`

#### Together AI Provider
```
/provider add
```
- Provider ID: `together`
- Provider name: `Together AI`
- Base URL: `https://api.together.xyz/v1`
- API Key: `your-together-key-here`
- Default model: `meta-llama/Llama-3-70b-chat-hf`
- Provider type: `openai-compatible`

### 11. Test all providers
```
/provider test groq
/provider test together
```

### 12. Switch between providers
```
/provider set-global groq
/provider list
/provider set-global together
/provider list
```

## Cleanup

### 13. Remove providers
```
/provider remove local-ollama
/provider remove groq
/provider remove together
/provider list
```

## Expected Behavior

1. **Provider Addition**: Should validate inputs and encrypt API keys
2. **Provider Listing**: Should show enabled/disabled status and default marker
3. **Provider Testing**: Should attempt connection and show latency
4. **Default Setting**: Should update the default provider correctly
5. **Provider Removal**: Should remove provider and update default if needed

## Error Cases to Test

1. **Invalid URL**: Try adding a provider with an invalid base URL
2. **Empty Fields**: Try adding a provider with missing required fields
3. **Duplicate ID**: Try adding a provider with an existing ID
4. **Non-existent Provider**: Try to show/test/remove a provider that doesn't exist
5. **Invalid Default**: Try to set a non-existent provider as default

## Integration with Agents

Once providers are configured, agents should automatically use the default provider or any configured overrides. You can test this by:

1. Setting up a provider
2. Running a normal Codebuff conversation
3. Checking that the agent uses your configured provider instead of the default

## Configuration Files

After adding providers, check these files:
- `~/.config/manicode/providers.json` - Global provider configuration (encrypted)
- `./codebuff.json` - Project-specific provider overrides (if configured)

## Troubleshooting

If commands don't work:
1. Make sure you're running the latest version with provider support
2. Check that the provider command is registered in the CLI
3. Look for error messages in the console
4. Verify file permissions for the config directory
