#!/usr/bin/env bun

/**
 * Test script for the provider system
 * Run with: bun run scripts/test-provider-system.ts
 */

import { ProviderStorage } from '../common/src/providers/storage'
import { ProviderConfig } from '../common/src/providers/types'
import { testProviderConnection, validateProviderConfig } from '../npm-app/src/providers/openrouter-integration'
import { tmpdir } from 'os'
import { join } from 'path'
import { mkdtempSync, rmSync } from 'fs'

async function testProviderSystem() {
  console.log('🧪 Testing Codebuff Provider System\n')

  // Create temporary directory for testing
  const tempDir = mkdtempSync(join(tmpdir(), 'codebuff-provider-test-'))
  console.log(`Using temp directory: ${tempDir}`)

  try {
    // Test 1: Create provider storage
    console.log('\n1. Testing provider storage creation...')
    const storage = new ProviderStorage(tempDir)
    console.log('✅ Provider storage created successfully')

    // Test 2: Add a test provider
    console.log('\n2. Testing provider addition...')
    const testProvider: ProviderConfig = {
      id: 'test-provider',
      name: 'Test Provider',
      baseUrl: 'https://api.openai.com/v1',
      apiKey: 'test-key-123',
      defaultModel: 'gpt-3.5-turbo',
      type: 'openai-compatible',
      enabled: true,
      description: 'Test provider for validation',
      availableModels: ['gpt-3.5-turbo', 'gpt-4'],
      headers: { 'X-Test': 'true' }
    }

    storage.addProvider(testProvider)
    console.log('✅ Provider added successfully')

    // Test 3: Retrieve provider
    console.log('\n3. Testing provider retrieval...')
    const retrievedProvider = storage.getProvider('test-provider')
    if (retrievedProvider && retrievedProvider.id === 'test-provider') {
      console.log('✅ Provider retrieved successfully')
    } else {
      throw new Error('Failed to retrieve provider')
    }

    // Test 4: List providers
    console.log('\n4. Testing provider listing...')
    const providers = storage.listProviders()
    if (providers.length === 1 && providers[0].id === 'test-provider') {
      console.log('✅ Provider listing works correctly')
    } else {
      throw new Error('Provider listing failed')
    }

    // Test 5: Set default provider
    console.log('\n5. Testing default provider setting...')
    storage.setDefaultProvider('test-provider')
    const defaultProvider = storage.getDefaultProvider()
    if (defaultProvider && defaultProvider.id === 'test-provider') {
      console.log('✅ Default provider set successfully')
    } else {
      throw new Error('Failed to set default provider')
    }

    // Test 6: Validate provider configuration
    console.log('\n6. Testing provider validation...')
    const validationErrors = validateProviderConfig(testProvider)
    if (validationErrors.length === 0) {
      console.log('✅ Provider validation passed')
    } else {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`)
    }

    // Test 7: Test invalid provider
    console.log('\n7. Testing invalid provider validation...')
    const invalidProvider: Partial<ProviderConfig> = {
      id: '',
      name: 'Invalid Provider',
      baseUrl: 'not-a-url',
      apiKey: '',
      defaultModel: '',
      type: 'openai-compatible'
    }
    const invalidErrors = validateProviderConfig(invalidProvider as ProviderConfig)
    if (invalidErrors.length > 0) {
      console.log('✅ Invalid provider correctly rejected')
      console.log(`   Errors: ${invalidErrors.join(', ')}`)
    } else {
      throw new Error('Invalid provider should have been rejected')
    }

    // Test 8: Resolve provider for agent
    console.log('\n8. Testing agent provider resolution...')
    const resolvedConfig = storage.resolveProviderForAgent('test-agent')
    if (resolvedConfig && resolvedConfig.provider.id === 'test-provider') {
      console.log('✅ Agent provider resolution works')
    } else {
      throw new Error('Agent provider resolution failed')
    }

    // Test 9: Remove provider
    console.log('\n9. Testing provider removal...')
    const removed = storage.removeProvider('test-provider')
    if (removed) {
      console.log('✅ Provider removed successfully')
    } else {
      throw new Error('Failed to remove provider')
    }

    // Test 10: Verify removal
    console.log('\n10. Testing provider removal verification...')
    const removedProvider = storage.getProvider('test-provider')
    if (!removedProvider) {
      console.log('✅ Provider removal verified')
    } else {
      throw new Error('Provider was not actually removed')
    }

    console.log('\n🎉 All provider system tests passed!')

  } catch (error) {
    console.error('\n❌ Test failed:', error instanceof Error ? error.message : String(error))
    process.exit(1)
  } finally {
    // Clean up temp directory
    try {
      rmSync(tempDir, { recursive: true, force: true })
      console.log(`\n🧹 Cleaned up temp directory: ${tempDir}`)
    } catch (error) {
      console.warn('Failed to clean up temp directory:', error)
    }
  }
}

// Test connection to a real provider (optional)
async function testRealProvider() {
  console.log('\n🌐 Testing real provider connection (optional)...')
  
  // This would test a real provider if API key is available
  const testProvider: ProviderConfig = {
    id: 'test-real',
    name: 'Test Real Provider',
    baseUrl: 'https://api.openai.com/v1',
    apiKey: process.env.OPENAI_API_KEY || 'dummy-key',
    defaultModel: 'gpt-3.5-turbo',
    type: 'openai-compatible',
    enabled: true
  }

  if (process.env.OPENAI_API_KEY) {
    try {
      const result = await testProviderConnection(testProvider)
      if (result.success) {
        console.log(`✅ Real provider connection successful (${result.latency}ms)`)
      } else {
        console.log(`⚠️  Real provider connection failed: ${result.error}`)
      }
    } catch (error) {
      console.log(`⚠️  Real provider test error: ${error instanceof Error ? error.message : String(error)}`)
    }
  } else {
    console.log('⏭️  Skipping real provider test (no OPENAI_API_KEY)')
  }
}

// Run tests
async function main() {
  await testProviderSystem()
  await testRealProvider()
  console.log('\n✨ Provider system testing complete!')
}

main().catch(console.error)
