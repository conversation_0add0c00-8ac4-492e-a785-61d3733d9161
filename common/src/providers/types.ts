import { z } from 'zod/v4'

/**
 * Configuration for a custom AI provider
 */
export const ProviderConfigSchema = z.object({
  /** Unique identifier for the provider */
  id: z.string().min(1, 'Provider ID is required'),
  
  /** Human-readable name for the provider */
  name: z.string().min(1, 'Provider name is required'),
  
  /** Base URL for the provider's API */
  baseUrl: z.string().url('Base URL must be a valid URL'),
  
  /** API key for authentication */
  apiKey: z.string().min(1, 'API key is required'),
  
  /** Default model ID to use with this provider */
  defaultModel: z.string().min(1, 'Default model is required'),
  
  /** Available models for this provider */
  availableModels: z.array(z.string()).optional().default([]),
  
  /** Additional headers to send with requests */
  headers: z.record(z.string()).optional().default({}),
  
  /** Provider type (openai-compatible, anthropic, etc.) */
  type: z.enum(['openai-compatible', 'anthropic', 'openrouter']).default('openai-compatible'),
  
  /** Whether this provider is enabled */
  enabled: z.boolean().default(true),
  
  /** Optional description */
  description: z.string().optional(),
})

export type ProviderConfig = z.infer<typeof ProviderConfigSchema>

/**
 * Global provider configuration
 */
export const GlobalProviderConfigSchema = z.object({
  /** Default provider to use when no specific provider is set */
  defaultProvider: z.string().optional(),
  
  /** Map of provider ID to provider configuration */
  providers: z.record(z.string(), ProviderConfigSchema).default({}),
})

export type GlobalProviderConfig = z.infer<typeof GlobalProviderConfigSchema>

/**
 * Per-agent provider override
 */
export const AgentProviderOverrideSchema = z.object({
  /** Provider ID to use for this agent */
  providerId: z.string(),
  
  /** Optional model override for this specific agent */
  modelId: z.string().optional(),
  
  /** Optional additional configuration for this agent */
  config: z.record(z.any()).optional().default({}),
})

export type AgentProviderOverride = z.infer<typeof AgentProviderOverrideSchema>

/**
 * Provider configuration that can be stored in codebuff.json
 */
export const CodebuffProviderConfigSchema = z.object({
  /** Global provider settings */
  global: GlobalProviderConfigSchema.optional(),
  
  /** Per-agent provider overrides */
  agents: z.record(z.string(), AgentProviderOverrideSchema).optional().default({}),
})

export type CodebuffProviderConfig = z.infer<typeof CodebuffProviderConfigSchema>

/**
 * Runtime provider configuration with resolved settings
 */
export interface ResolvedProviderConfig {
  /** The provider configuration */
  provider: ProviderConfig
  
  /** The model to use */
  modelId: string
  
  /** Additional runtime configuration */
  runtimeConfig: Record<string, any>
}

/**
 * Provider management commands
 */
export type ProviderCommand = 
  | 'add'
  | 'remove' 
  | 'list'
  | 'set-global'
  | 'set-agent'
  | 'unset-agent'
  | 'test'
  | 'show'

/**
 * Provider command context
 */
export interface ProviderCommandContext {
  command: ProviderCommand
  args: string[]
  providerId?: string
  agentId?: string
}
