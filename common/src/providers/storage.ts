import { existsSync, readFileSync, writeFileSync } from 'fs'
import path from 'path'
import { z } from 'zod/v4'

import { encryptApiKeyInternal, decryptApiKeyInternal } from '../api-keys/crypto'
import { logger } from '../util/logger'
import { ensureDirectoryExists } from '../util/file'
import { 
  ProviderConfig, 
  ProviderConfigSchema, 
  GlobalProviderConfig,
  GlobalProviderConfigSchema,
  CodebuffProviderConfig,
  CodebuffProviderConfigSchema,
  ResolvedProviderConfig,
  AgentProviderOverride
} from './types'

/**
 * Storage for provider configurations
 */
export class ProviderStorage {
  private configPath: string
  private globalConfig: GlobalProviderConfig
  private projectConfig: CodebuffProviderConfig | null = null

  constructor(configDir: string, projectRoot?: string) {
    this.configPath = path.join(configDir, 'providers.json')
    this.globalConfig = this.loadGlobalConfig()
    
    if (projectRoot) {
      this.loadProjectConfig(projectRoot)
    }
  }

  /**
   * Load global provider configuration from user config directory
   */
  private loadGlobalConfig(): GlobalProviderConfig {
    if (!existsSync(this.configPath)) {
      return { providers: {} }
    }

    try {
      const content = readFileSync(this.configPath, 'utf-8')
      const parsed = JSON.parse(content)
      
      // Decrypt API keys
      const decryptedProviders: Record<string, ProviderConfig> = {}
      for (const [id, provider] of Object.entries(parsed.providers || {})) {
        const providerData = provider as any
        if (providerData.apiKey) {
          try {
            const decryptedKey = decryptApiKeyInternal(providerData.apiKey)
            if (decryptedKey) {
              providerData.apiKey = decryptedKey
            }
          } catch (error) {
            logger.warn({ providerId: id, error }, 'Failed to decrypt provider API key')
            continue
          }
        }
        decryptedProviders[id] = providerData
      }

      const result = GlobalProviderConfigSchema.safeParse({
        ...parsed,
        providers: decryptedProviders
      })

      if (!result.success) {
        logger.warn({ error: result.error }, 'Invalid global provider configuration')
        return { providers: {} }
      }

      return result.data
    } catch (error) {
      logger.error({ error, configPath: this.configPath }, 'Failed to load global provider config')
      return { providers: {} }
    }
  }

  /**
   * Load project-specific provider configuration from codebuff.json
   */
  private loadProjectConfig(projectRoot: string): void {
    const codebuffConfigPath = path.join(projectRoot, 'codebuff.json')
    const codebuffConfigcPath = path.join(projectRoot, 'codebuff.jsonc')
    
    const configPath = existsSync(codebuffConfigcPath) 
      ? codebuffConfigcPath 
      : existsSync(codebuffConfigPath) 
        ? codebuffConfigPath 
        : null

    if (!configPath) {
      this.projectConfig = null
      return
    }

    try {
      const content = readFileSync(configPath, 'utf-8')
      const parsed = JSON.parse(content)
      
      if (parsed.providers) {
        const result = CodebuffProviderConfigSchema.safeParse(parsed.providers)
        if (result.success) {
          this.projectConfig = result.data
        } else {
          logger.warn({ error: result.error }, 'Invalid project provider configuration')
          this.projectConfig = null
        }
      } else {
        this.projectConfig = null
      }
    } catch (error) {
      logger.error({ error, configPath }, 'Failed to load project provider config')
      this.projectConfig = null
    }
  }

  /**
   * Save global provider configuration
   */
  private saveGlobalConfig(): void {
    try {
      ensureDirectoryExists(path.dirname(this.configPath))
      
      // Encrypt API keys before saving
      const encryptedProviders: Record<string, any> = {}
      for (const [id, provider] of Object.entries(this.globalConfig.providers)) {
        const providerData = { ...provider }
        if (providerData.apiKey) {
          try {
            providerData.apiKey = encryptApiKeyInternal(providerData.apiKey)
          } catch (error) {
            logger.error({ providerId: id, error }, 'Failed to encrypt provider API key')
            throw error
          }
        }
        encryptedProviders[id] = providerData
      }

      const configToSave = {
        ...this.globalConfig,
        providers: encryptedProviders
      }

      writeFileSync(this.configPath, JSON.stringify(configToSave, null, 2))
      logger.info({ configPath: this.configPath }, 'Saved global provider configuration')
    } catch (error) {
      logger.error({ error, configPath: this.configPath }, 'Failed to save global provider config')
      throw error
    }
  }

  /**
   * Add or update a provider
   */
  addProvider(provider: ProviderConfig): void {
    const result = ProviderConfigSchema.safeParse(provider)
    if (!result.success) {
      throw new Error(`Invalid provider configuration: ${result.error.message}`)
    }

    this.globalConfig.providers[provider.id] = result.data
    this.saveGlobalConfig()
  }

  /**
   * Remove a provider
   */
  removeProvider(providerId: string): boolean {
    if (!(providerId in this.globalConfig.providers)) {
      return false
    }

    delete this.globalConfig.providers[providerId]
    this.saveGlobalConfig()
    return true
  }

  /**
   * Get a provider by ID
   */
  getProvider(providerId: string): ProviderConfig | null {
    return this.globalConfig.providers[providerId] || null
  }

  /**
   * List all providers
   */
  listProviders(): ProviderConfig[] {
    return Object.values(this.globalConfig.providers)
  }

  /**
   * Set default provider
   */
  setDefaultProvider(providerId: string): void {
    if (!(providerId in this.globalConfig.providers)) {
      throw new Error(`Provider '${providerId}' not found`)
    }

    this.globalConfig.defaultProvider = providerId
    this.saveGlobalConfig()
  }

  /**
   * Get default provider
   */
  getDefaultProvider(): ProviderConfig | null {
    if (!this.globalConfig.defaultProvider) {
      return null
    }
    return this.getProvider(this.globalConfig.defaultProvider)
  }

  /**
   * Resolve provider configuration for an agent
   */
  resolveProviderForAgent(agentId?: string): ResolvedProviderConfig | null {
    let provider: ProviderConfig | null = null
    let modelId: string | null = null
    let runtimeConfig: Record<string, any> = {}

    // Check project-level agent override first
    if (agentId && this.projectConfig?.agents?.[agentId]) {
      const override = this.projectConfig.agents[agentId]
      provider = this.getProvider(override.providerId)
      modelId = override.modelId || null
      runtimeConfig = { ...runtimeConfig, ...override.config }
    }

    // Fall back to project default provider
    if (!provider && this.projectConfig?.global?.defaultProvider) {
      provider = this.getProvider(this.projectConfig.global.defaultProvider)
    }

    // Fall back to global default provider
    if (!provider) {
      provider = this.getDefaultProvider()
    }

    if (!provider) {
      return null
    }

    return {
      provider,
      modelId: modelId || provider.defaultModel,
      runtimeConfig
    }
  }

  /**
   * Get global configuration
   */
  getGlobalConfig(): GlobalProviderConfig {
    return { ...this.globalConfig }
  }

  /**
   * Get project configuration
   */
  getProjectConfig(): CodebuffProviderConfig | null {
    return this.projectConfig ? { ...this.projectConfig } : null
  }
}
