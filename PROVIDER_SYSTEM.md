# Codebuff Provider System

The Codebuff Provider System allows you to configure custom AI providers and models for your agents, giving you full control over which models and endpoints are used for different tasks.

## Features

- **Custom Providers**: Configure any OpenAI-compatible API endpoint
- **Per-Agent Configuration**: Set different providers/models for specific agents
- **Global Defaults**: Set a default provider for all agents
- **Secure Storage**: API keys are encrypted and stored securely
- **Provider Testing**: Test connections and fetch available models
- **Multiple Provider Types**: Support for OpenAI-compatible, Anthropic, and OpenRouter APIs

## Quick Start

### 1. Add a Provider

Use the interactive command to add a new provider:

```bash
/provider add
```

This will prompt you for:
- Provider ID (e.g., "my-openai")
- Provider name (e.g., "My OpenAI Instance")
- Base URL (e.g., "https://api.openai.com/v1")
- API key
- Default model (e.g., "gpt-4")
- Provider type (openai-compatible, anthropic, openrouter)
- Description (optional)

### 2. List Providers

```bash
/provider list
```

### 3. Set Default Provider

```bash
/provider set-global my-openai
```

### 4. Test Provider

```bash
/provider test my-openai
```

## Provider Types

### OpenAI-Compatible
Most providers use this type, including:
- OpenAI API
- Local Ollama instances
- Groq
- Together AI
- Many other providers

### Anthropic
For direct Anthropic API access:
- Claude models via Anthropic API

### OpenRouter
For OpenRouter.ai service:
- Access to multiple models through OpenRouter

## Configuration Examples

### Local Ollama
```bash
/provider add
# Provider ID: local-ollama
# Provider name: Local Ollama
# Base URL: http://localhost:11434/v1
# API key: dummy-key
# Default model: llama3.2
# Provider type: openai-compatible
```

### Groq
```bash
/provider add
# Provider ID: groq
# Provider name: Groq
# Base URL: https://api.groq.com/openai/v1
# API key: gsk_your-api-key-here
# Default model: llama-3.1-70b-versatile
# Provider type: openai-compatible
```

### Together AI
```bash
/provider add
# Provider ID: together
# Provider name: Together AI
# Base URL: https://api.together.xyz/v1
# API key: your-api-key-here
# Default model: meta-llama/Llama-3-70b-chat-hf
# Provider type: openai-compatible
```

## Project Configuration

You can also configure providers in your `codebuff.json` file:

```json
{
  "providers": {
    "global": {
      "defaultProvider": "my-openai",
      "providers": {
        "my-openai": {
          "id": "my-openai",
          "name": "My OpenAI",
          "baseUrl": "https://api.openai.com/v1",
          "apiKey": "sk-proj-your-key-here",
          "defaultModel": "gpt-4",
          "type": "openai-compatible",
          "enabled": true
        }
      }
    },
    "agents": {
      "code-reviewer": {
        "providerId": "my-openai",
        "modelId": "gpt-4-turbo"
      }
    }
  }
}
```

## Commands Reference

| Command | Description |
|---------|-------------|
| `/provider add` | Add a new provider interactively |
| `/provider list` | List all configured providers |
| `/provider show <id>` | Show detailed provider information |
| `/provider remove <id>` | Remove a provider |
| `/provider set-global <id>` | Set default provider |
| `/provider test <id>` | Test provider connection |
| `/provider set-agent <provider-id> <agent-id>` | Set provider for specific agent (coming soon) |
| `/provider unset-agent <agent-id>` | Remove provider override for agent (coming soon) |

## Security

- API keys are encrypted using AES-256-GCM encryption
- Keys are stored in your user config directory (`~/.config/manicode/providers.json`)
- Never commit API keys to version control
- Use environment variables for sensitive deployments

## Troubleshooting

### Provider Test Fails
1. Check the base URL is correct
2. Verify your API key is valid
3. Ensure the provider supports the `/models` endpoint
4. Check network connectivity

### Model Not Found
1. Use `/provider test <id>` to see available models
2. Update the `defaultModel` to a supported model
3. Check the model name format (some providers use different naming)

### Agent Not Using Custom Provider
1. Verify the provider is set as default with `/provider set-global <id>`
2. Check that the agent doesn't have a hardcoded model override
3. Restart Codebuff after configuration changes

## Coming Soon

- **Per-Agent Configuration**: Full support for setting different providers per agent
- **Model Switching**: Dynamic model switching during conversations
- **Provider Templates**: Pre-configured templates for popular providers
- **Usage Analytics**: Track usage across different providers
- **Cost Tracking**: Monitor costs across different providers

## Examples

See `examples/provider-config-example.json` for a complete configuration example.

## Support

If you encounter issues with the provider system, please:
1. Check the logs for error messages
2. Test your provider configuration with `/provider test <id>`
3. Verify your API keys and endpoints
4. Report issues with detailed error messages
