{"description": "Example Codebuff configuration with custom AI providers", "providers": {"global": {"defaultProvider": "my-openai", "providers": {"my-openai": {"id": "my-openai", "name": "My OpenAI Instance", "baseUrl": "https://api.openai.com/v1", "apiKey": "sk-proj-your-api-key-here", "defaultModel": "gpt-4", "type": "openai-compatible", "enabled": true, "description": "Custom OpenAI configuration", "availableModels": ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"], "headers": {"X-Custom-Header": "value"}}, "local-ollama": {"id": "local-ollama", "name": "Local Ollama", "baseUrl": "http://localhost:11434/v1", "apiKey": "dummy-key", "defaultModel": "llama3.2", "type": "openai-compatible", "enabled": true, "description": "Local Ollama instance", "availableModels": ["llama3.2", "codellama", "mistral"]}, "anthropic-custom": {"id": "anthropic-custom", "name": "Custom Anthropic", "baseUrl": "https://api.anthropic.com/v1", "apiKey": "sk-ant-your-api-key-here", "defaultModel": "claude-3-sonnet-20240229", "type": "anthropic", "enabled": true, "description": "Custom Anthropic configuration"}}}, "agents": {"code-reviewer": {"providerId": "anthropic-custom", "modelId": "claude-3-opus-20240229", "config": {"temperature": 0.1}}, "local-tester": {"providerId": "local-ollama", "modelId": "codellama", "config": {"temperature": 0.2}}}}, "baseAgent": "base", "spawnableAgents": ["code-reviewer", "local-tester", "file-picker", "researcher"]}