# 🚀 Provider System Demo

Once you have Codebuff running (after installing Bun and following the CONTRIBUTING.md setup), here's exactly how the new provider system will work:

## 🎯 Quick Demo Flow

### 1. Start Codebuff
```bash
# After full setup from CONTRIBUTING.md
bun run start-bin
```

### 2. Check Available Commands
```bash
# In Codebuff CLI
/provider
```

You'll see:
```
🔧 Provider Management Commands:

/provider add           - Add a new provider interactively
/provider list          - List all configured providers
/provider show <id>     - Show detailed provider information
/provider remove <id>   - Remove a provider
/provider set-global <id> - Set default provider
/provider test <id>     - Test provider connection

Examples:
  /provider add
  /provider list
  /provider show my-openai
  /provider set-global my-openai
```

### 3. Add Your First Provider
```bash
/provider add
```

Interactive prompts:
```
🔧 Add New Provider
Enter the provider details:
Provider ID (e.g., "my-openai"): local-ollama
Provider name (e.g., "My OpenAI"): Local Ollama
Base URL (e.g., "https://api.openai.com/v1"): http://localhost:11434/v1
API Key: dummy-key
Default model (e.g., "gpt-4"): llama3.2
Provider type options: openai-compatible, anthropic, openrouter
Provider type [openai-compatible]: openai-compatible
Description (optional): Local Ollama instance
✅ Provider 'local-ollama' added successfully!
Test provider connection? (y/N): y
Testing provider connection...
✅ Provider test successful (150ms)
Set as default provider? (y/N): y
✅ Set 'local-ollama' as default provider
```

### 4. List Your Providers
```bash
/provider list
```

Output:
```
🔧 Configured Providers:

✓ local-ollama (default)
   Name: Local Ollama
   Type: openai-compatible
   URL:  http://localhost:11434/v1
   Model: llama3.2
   Description: Local Ollama instance
```

### 5. Test Provider Connection
```bash
/provider test local-ollama
```

Output:
```
Testing provider 'local-ollama'...
✅ Connection successful (89ms)
Fetching available models...
✅ Found 5 models
Available models:
  ● llama3.2 (default)
  ○ codellama
  ○ mistral
  ○ phi3
  ○ gemma2
```

### 6. Use Your Custom Provider
Now when you chat with Codebuff, it will automatically use your configured provider:

```bash
# Regular Codebuff conversation
create a simple calculator function
```

Behind the scenes:
- Codebuff loads your provider configuration
- Uses `http://localhost:11434/v1` instead of default endpoints
- Sends requests to your local Ollama with `llama3.2` model
- Your API key and headers are automatically included

## 🔧 Advanced Examples

### Multiple Providers
```bash
# Add OpenAI
/provider add
# Provider ID: openai-gpt4
# Base URL: https://api.openai.com/v1
# API Key: sk-proj-your-key
# Model: gpt-4

# Add Groq for fast inference
/provider add  
# Provider ID: groq-fast
# Base URL: https://api.groq.com/openai/v1
# API Key: gsk_your-groq-key
# Model: llama-3.1-70b-versatile

# Switch between them
/provider set-global groq-fast
/provider set-global openai-gpt4
```

### Provider Testing
```bash
/provider test openai-gpt4
```
```
Testing provider 'openai-gpt4'...
✅ Connection successful (245ms)
Fetching available models...
✅ Found 12 models
Available models:
  ○ gpt-3.5-turbo
  ● gpt-4 (default)
  ○ gpt-4-turbo
  ○ gpt-4o
  ... and 8 more
```

## 📁 Configuration Files

### Global Config (`~/.config/manicode/providers.json`)
```json
{
  "defaultProvider": "local-ollama",
  "providers": {
    "local-ollama": {
      "id": "local-ollama",
      "name": "Local Ollama",
      "baseUrl": "http://localhost:11434/v1",
      "apiKey": "[ENCRYPTED]",
      "defaultModel": "llama3.2",
      "type": "openai-compatible",
      "enabled": true
    }
  }
}
```

### Project Config (`./codebuff.json`)
```json
{
  "providers": {
    "global": {
      "defaultProvider": "local-ollama"
    },
    "agents": {
      "code-reviewer": {
        "providerId": "openai-gpt4",
        "modelId": "gpt-4-turbo"
      }
    }
  }
}
```

## 🎉 Benefits You Get

1. **Cost Control**: Use cheaper/free local models for development
2. **Speed**: Use fast providers like Groq for quick iterations  
3. **Privacy**: Keep sensitive code on local models
4. **Flexibility**: Switch providers per project or agent
5. **No Vendor Lock-in**: Use any OpenAI-compatible API

## 🚀 Ready to Try?

1. Install Bun: `curl -fsSL https://bun.sh/install | bash`
2. Follow the full setup in `CONTRIBUTING.md`
3. Start Codebuff: `bun run start-bin`
4. Try the provider commands above!

The provider system is fully integrated and ready to use! 🎯
