import { createOpenRouter } from '@codebuff/internal/openrouter-ai-sdk/provider'
import { ProviderConfig, ResolvedProviderConfig } from '@codebuff/common/providers/types'
import { logger } from '../utils/logger'

import type { LanguageModelV2 } from '@ai-sdk/provider'

/**
 * Create an OpenRouter-compatible provider from a custom provider configuration
 */
export function createCustomProvider(
  resolvedConfig: ResolvedProviderConfig
): LanguageModelV2 | null {
  try {
    const { provider, modelId, runtimeConfig } = resolvedConfig

    // Create headers with API key and any custom headers
    const headers: Record<string, string> = {
      'Authorization': `Bearer ${provider.apiKey}`,
      ...provider.headers
    }

    // Add any runtime config headers
    if (runtimeConfig.headers) {
      Object.assign(headers, runtimeConfig.headers)
    }

    // Create the provider based on type
    switch (provider.type) {
      case 'openrouter':
        // Use the existing OpenRouter provider but with custom base URL if needed
        return createOpenRouter({
          baseURL: provider.baseUrl,
          apiKey: provider.apiKey,
          headers: provider.headers
        })(modelId)

      case 'openai-compatible':
      case 'anthropic':
        // For OpenAI-compatible and Anthropic providers, use OpenRouter with custom base URL
        return createOpenRouter({
          baseURL: provider.baseUrl,
          apiKey: provider.apiKey,
          headers
        })(modelId)

      default:
        logger.warn({ providerType: provider.type }, 'Unsupported provider type')
        return null
    }
  } catch (error) {
    logger.error({ error, providerId: resolvedConfig.provider.id }, 'Failed to create custom provider')
    return null
  }
}

/**
 * Get model configuration for a provider
 */
export function getModelConfig(resolvedConfig: ResolvedProviderConfig): any {
  const { provider, modelId, runtimeConfig } = resolvedConfig

  const config: any = {
    model: modelId,
    baseURL: provider.baseUrl,
    apiKey: provider.apiKey,
    headers: {
      ...provider.headers,
      ...runtimeConfig.headers
    }
  }

  // Add provider-specific configurations
  if (provider.type === 'openrouter' && runtimeConfig.reasoning) {
    config.reasoning = runtimeConfig.reasoning
  }

  return config
}

/**
 * Validate provider configuration
 */
export function validateProviderConfig(provider: ProviderConfig): string[] {
  const errors: string[] = []

  if (!provider.id) {
    errors.push('Provider ID is required')
  }

  if (!provider.name) {
    errors.push('Provider name is required')
  }

  if (!provider.baseUrl) {
    errors.push('Base URL is required')
  } else {
    try {
      new URL(provider.baseUrl)
    } catch {
      errors.push('Base URL must be a valid URL')
    }
  }

  if (!provider.apiKey) {
    errors.push('API key is required')
  }

  if (!provider.defaultModel) {
    errors.push('Default model is required')
  }

  return errors
}

/**
 * Test provider connection
 */
export async function testProviderConnection(
  provider: ProviderConfig
): Promise<{ success: boolean; error?: string; latency?: number }> {
  const startTime = Date.now()

  try {
    // Create a simple test request to validate the provider
    const response = await fetch(`${provider.baseUrl}/models`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${provider.apiKey}`,
        'Content-Type': 'application/json',
        ...provider.headers
      }
    })

    const latency = Date.now() - startTime

    if (response.ok) {
      return { success: true, latency }
    } else {
      const errorText = await response.text()
      return { 
        success: false, 
        error: `HTTP ${response.status}: ${errorText}`,
        latency 
      }
    }
  } catch (error) {
    const latency = Date.now() - startTime
    return { 
      success: false, 
      error: error instanceof Error ? error.message : String(error),
      latency 
    }
  }
}

/**
 * Get available models from a provider
 */
export async function getProviderModels(
  provider: ProviderConfig
): Promise<{ success: boolean; models?: string[]; error?: string }> {
  try {
    const response = await fetch(`${provider.baseUrl}/models`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${provider.apiKey}`,
        'Content-Type': 'application/json',
        ...provider.headers
      }
    })

    if (!response.ok) {
      const errorText = await response.text()
      return { 
        success: false, 
        error: `HTTP ${response.status}: ${errorText}` 
      }
    }

    const data = await response.json()
    
    // Handle different response formats
    let models: string[] = []
    
    if (Array.isArray(data)) {
      // Direct array of models
      models = data.map(model => 
        typeof model === 'string' ? model : model.id || model.name
      ).filter(Boolean)
    } else if (data.data && Array.isArray(data.data)) {
      // OpenAI-style response with data array
      models = data.data.map((model: any) => model.id || model.name).filter(Boolean)
    } else if (data.models && Array.isArray(data.models)) {
      // Alternative format
      models = data.models.map((model: any) => 
        typeof model === 'string' ? model : model.id || model.name
      ).filter(Boolean)
    }

    return { success: true, models }
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : String(error) 
    }
  }
}

/**
 * Format provider for display
 */
export function formatProviderForDisplay(provider: ProviderConfig): string {
  const status = provider.enabled ? '✓' : '✗'
  const type = provider.type.toUpperCase()
  return `${status} ${provider.id} (${type}) - ${provider.name}`
}

/**
 * Get provider type display name
 */
export function getProviderTypeDisplayName(type: string): string {
  switch (type) {
    case 'openai-compatible':
      return 'OpenAI Compatible'
    case 'anthropic':
      return 'Anthropic'
    case 'openrouter':
      return 'OpenRouter'
    default:
      return type
  }
}
