import { ProviderStorage } from '@codebuff/common/providers/storage'
import { ResolvedProviderConfig, ProviderConfig } from '@codebuff/common/providers/types'
import { CONFIG_DIR } from '../credentials'
import { getProjectRoot } from '../project-files'
import { logger } from '../utils/logger'

/**
 * Provider resolver for agents
 */
export class ProviderResolver {
  private storage: ProviderStorage

  constructor() {
    const projectRoot = getProjectRoot()
    this.storage = new ProviderStorage(CONFIG_DIR, projectRoot)
  }

  /**
   * Resolve provider configuration for an agent
   */
  resolveForAgent(agentId?: string): ResolvedProviderConfig | null {
    try {
      return this.storage.resolveProviderForAgent(agentId)
    } catch (error) {
      logger.error({ error, agentId }, 'Failed to resolve provider for agent')
      return null
    }
  }

  /**
   * Get provider by ID
   */
  getProvider(providerId: string): ProviderConfig | null {
    try {
      return this.storage.getProvider(providerId)
    } catch (error) {
      logger.error({ error, providerId }, 'Failed to get provider')
      return null
    }
  }

  /**
   * Get default provider
   */
  getDefaultProvider(): ProviderConfig | null {
    try {
      return this.storage.getDefaultProvider()
    } catch (error) {
      logger.error({ error }, 'Failed to get default provider')
      return null
    }
  }

  /**
   * Check if any providers are configured
   */
  hasProviders(): boolean {
    try {
      return this.storage.listProviders().length > 0
    } catch (error) {
      logger.error({ error }, 'Failed to check for providers')
      return false
    }
  }

  /**
   * Get storage instance for direct access
   */
  getStorage(): ProviderStorage {
    return this.storage
  }
}

// Global instance
let providerResolver: ProviderResolver | null = null

/**
 * Get global provider resolver instance
 */
export function getProviderResolver(): ProviderResolver {
  if (!providerResolver) {
    providerResolver = new ProviderResolver()
  }
  return providerResolver
}

/**
 * Reset provider resolver (useful for testing or config changes)
 */
export function resetProviderResolver(): void {
  providerResolver = null
}
