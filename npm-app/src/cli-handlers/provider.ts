import { cyan, green, red, yellow, bold } from 'picocolors'
import readline from 'readline'

import { ProviderStorage } from '@codebuff/common/providers/storage'
import { ProviderConfig, ProviderCommandContext } from '@codebuff/common/providers/types'
import { CONFIG_DIR } from '../credentials'
import { getProjectRoot } from '../project-files'
import { logger } from '../utils/logger'
import { testProviderConnection, getProviderModels, validateProviderConfig } from '../providers/openrouter-integration'

let providerStorage: ProviderStorage | null = null

/**
 * Get or create provider storage instance
 */
function getProviderStorage(): ProviderStorage {
  if (!providerStorage) {
    const projectRoot = getProjectRoot()
    providerStorage = new ProviderStorage(CONFIG_DIR, projectRoot)
  }
  return providerStorage
}

/**
 * Parse provider command input
 */
export function parseProviderCommand(input: string): ProviderCommandContext | null {
  const parts = input.trim().split(/\s+/)
  if (parts.length < 2 || parts[0] !== 'provider') {
    return null
  }

  const command = parts[1] as any
  const args = parts.slice(2)

  const validCommands = ['add', 'remove', 'list', 'set-global', 'set-agent', 'unset-agent', 'test', 'show']
  if (!validCommands.includes(command)) {
    return null
  }

  return {
    command,
    args,
    providerId: args[0],
    agentId: command === 'set-agent' || command === 'unset-agent' ? args[1] : undefined
  }
}

/**
 * Handle provider commands
 */
export async function handleProviderCommand(
  context: ProviderCommandContext,
  rl: readline.Interface,
  freshPrompt: () => void
): Promise<void> {
  try {
    const storage = getProviderStorage()

    switch (context.command) {
      case 'add':
        await handleAddProvider(storage, rl, freshPrompt)
        break
      case 'remove':
        await handleRemoveProvider(storage, context.providerId, freshPrompt)
        break
      case 'list':
        await handleListProviders(storage, freshPrompt)
        break
      case 'set-global':
        await handleSetGlobalProvider(storage, context.providerId, freshPrompt)
        break
      case 'show':
        await handleShowProvider(storage, context.providerId, freshPrompt)
        break
      case 'test':
        await handleTestProvider(storage, context.providerId, freshPrompt)
        break
      case 'set-agent':
        await handleSetAgentProvider(storage, context.providerId, context.agentId, freshPrompt)
        break
      case 'unset-agent':
        await handleUnsetAgentProvider(storage, context.agentId, freshPrompt)
        break
      default:
        console.log(yellow(`Unknown provider command: ${context.command}`))
        showProviderHelp()
        freshPrompt()
    }
  } catch (error) {
    logger.error({ error, context }, 'Provider command failed')
    console.log(red(`Error: ${error instanceof Error ? error.message : String(error)}`))
    freshPrompt()
  }
}

/**
 * Interactive provider addition
 */
async function handleAddProvider(
  storage: ProviderStorage,
  rl: readline.Interface,
  freshPrompt: () => void
): Promise<void> {
  console.log(cyan('\n🔧 Add New Provider'))
  console.log('Enter the provider details:')

  const provider: Partial<ProviderConfig> = {}

  // Get provider ID
  provider.id = await askQuestion(rl, 'Provider ID (e.g., "my-openai"): ')
  if (!provider.id) {
    console.log(red('Provider ID is required'))
    freshPrompt()
    return
  }

  // Check if provider already exists
  if (storage.getProvider(provider.id)) {
    const overwrite = await askQuestion(rl, `Provider '${provider.id}' already exists. Overwrite? (y/N): `)
    if (overwrite.toLowerCase() !== 'y' && overwrite.toLowerCase() !== 'yes') {
      console.log(yellow('Cancelled'))
      freshPrompt()
      return
    }
  }

  // Get provider name
  provider.name = await askQuestion(rl, 'Provider name (e.g., "My OpenAI"): ')
  if (!provider.name) {
    console.log(red('Provider name is required'))
    freshPrompt()
    return
  }

  // Get base URL
  provider.baseUrl = await askQuestion(rl, 'Base URL (e.g., "https://api.openai.com/v1"): ')
  if (!provider.baseUrl) {
    console.log(red('Base URL is required'))
    freshPrompt()
    return
  }

  // Get API key
  provider.apiKey = await askQuestion(rl, 'API Key: ', true)
  if (!provider.apiKey) {
    console.log(red('API Key is required'))
    freshPrompt()
    return
  }

  // Get default model
  provider.defaultModel = await askQuestion(rl, 'Default model (e.g., "gpt-4"): ')
  if (!provider.defaultModel) {
    console.log(red('Default model is required'))
    freshPrompt()
    return
  }

  // Get provider type
  const typeOptions = ['openai-compatible', 'anthropic', 'openrouter']
  console.log(`Provider type options: ${typeOptions.join(', ')}`)
  const typeInput = await askQuestion(rl, 'Provider type [openai-compatible]: ')
  provider.type = (typeInput && typeOptions.includes(typeInput as any))
    ? typeInput as any
    : 'openai-compatible'

  // Get description (optional)
  provider.description = await askQuestion(rl, 'Description (optional): ')

  // Validate the provider configuration
  const validationErrors = validateProviderConfig(provider as ProviderConfig)
  if (validationErrors.length > 0) {
    console.log(red('Validation errors:'))
    validationErrors.forEach(error => console.log(red(`  - ${error}`)))
    freshPrompt()
    return
  }

  try {
    storage.addProvider(provider as ProviderConfig)
    console.log(green(`✅ Provider '${provider.id}' added successfully!`))

    // Ask if they want to test the provider
    const testProvider = await askQuestion(rl, 'Test provider connection? (y/N): ')
    if (testProvider.toLowerCase() === 'y' || testProvider.toLowerCase() === 'yes') {
      console.log(yellow('Testing provider connection...'))
      const testResult = await testProviderConnection(provider as ProviderConfig)
      if (testResult.success) {
        console.log(green(`✅ Provider test successful (${testResult.latency}ms)`))
      } else {
        console.log(red(`❌ Provider test failed: ${testResult.error}`))
      }
    }

    // Ask if they want to set it as default
    const setDefault = await askQuestion(rl, 'Set as default provider? (y/N): ')
    if (setDefault.toLowerCase() === 'y' || setDefault.toLowerCase() === 'yes') {
      storage.setDefaultProvider(provider.id!)
      console.log(green(`✅ Set '${provider.id}' as default provider`))
    }
  } catch (error) {
    console.log(red(`Failed to add provider: ${error instanceof Error ? error.message : String(error)}`))
  }

  freshPrompt()
}

/**
 * Remove a provider
 */
async function handleRemoveProvider(
  storage: ProviderStorage,
  providerId: string | undefined,
  freshPrompt: () => void
): Promise<void> {
  if (!providerId) {
    console.log(red('Provider ID is required'))
    showProviderHelp()
    freshPrompt()
    return
  }

  const provider = storage.getProvider(providerId)
  if (!provider) {
    console.log(red(`Provider '${providerId}' not found`))
    freshPrompt()
    return
  }

  const success = storage.removeProvider(providerId)
  if (success) {
    console.log(green(`✅ Provider '${providerId}' removed successfully`))
  } else {
    console.log(red(`Failed to remove provider '${providerId}'`))
  }

  freshPrompt()
}

/**
 * List all providers
 */
async function handleListProviders(
  storage: ProviderStorage,
  freshPrompt: () => void
): Promise<void> {
  const providers = storage.listProviders()
  const defaultProvider = storage.getDefaultProvider()

  if (providers.length === 0) {
    console.log(yellow('No providers configured'))
    console.log('Use /provider add to add a new provider')
    freshPrompt()
    return
  }

  console.log(cyan('\n🔧 Configured Providers:'))
  console.log()

  providers.forEach(provider => {
    const isDefault = defaultProvider?.id === provider.id
    const status = provider.enabled ? green('✓') : red('✗')
    const defaultMark = isDefault ? yellow(' (default)') : ''

    console.log(`${status} ${bold(provider.id)}${defaultMark}`)
    console.log(`   Name: ${provider.name}`)
    console.log(`   Type: ${provider.type}`)
    console.log(`   URL:  ${provider.baseUrl}`)
    console.log(`   Model: ${provider.defaultModel}`)
    if (provider.description) {
      console.log(`   Description: ${provider.description}`)
    }
    console.log()
  })

  freshPrompt()
}

/**
 * Set global default provider
 */
async function handleSetGlobalProvider(
  storage: ProviderStorage,
  providerId: string | undefined,
  freshPrompt: () => void
): Promise<void> {
  if (!providerId) {
    console.log(red('Provider ID is required'))
    showProviderHelp()
    freshPrompt()
    return
  }

  try {
    storage.setDefaultProvider(providerId)
    console.log(green(`✅ Set '${providerId}' as default provider`))
  } catch (error) {
    console.log(red(`Failed to set default provider: ${error instanceof Error ? error.message : String(error)}`))
  }

  freshPrompt()
}

/**
 * Show detailed provider information
 */
async function handleShowProvider(
  storage: ProviderStorage,
  providerId: string | undefined,
  freshPrompt: () => void
): Promise<void> {
  if (!providerId) {
    console.log(red('Provider ID is required'))
    showProviderHelp()
    freshPrompt()
    return
  }

  const provider = storage.getProvider(providerId)
  if (!provider) {
    console.log(red(`Provider '${providerId}' not found`))
    freshPrompt()
    return
  }

  const isDefault = storage.getDefaultProvider()?.id === provider.id

  console.log(cyan(`\n🔧 Provider: ${bold(provider.id)}`))
  console.log(`Name: ${provider.name}`)
  console.log(`Type: ${provider.type}`)
  console.log(`Base URL: ${provider.baseUrl}`)
  console.log(`Default Model: ${provider.defaultModel}`)
  console.log(`Enabled: ${provider.enabled ? green('Yes') : red('No')}`)
  console.log(`Default Provider: ${isDefault ? green('Yes') : 'No'}`)

  if (provider.description) {
    console.log(`Description: ${provider.description}`)
  }

  if (provider.availableModels && provider.availableModels.length > 0) {
    console.log(`Available Models: ${provider.availableModels.join(', ')}`)
  }

  if (Object.keys(provider.headers || {}).length > 0) {
    console.log(`Custom Headers: ${Object.keys(provider.headers!).join(', ')}`)
  }

  freshPrompt()
}

/**
 * Test provider connection
 */
async function handleTestProvider(
  storage: ProviderStorage,
  providerId: string | undefined,
  freshPrompt: () => void
): Promise<void> {
  if (!providerId) {
    console.log(red('Provider ID is required'))
    showProviderHelp()
    freshPrompt()
    return
  }

  const provider = storage.getProvider(providerId)
  if (!provider) {
    console.log(red(`Provider '${providerId}' not found`))
    freshPrompt()
    return
  }

  console.log(yellow(`Testing provider '${providerId}'...`))

  try {
    // Test basic connection
    const testResult = await testProviderConnection(provider)

    if (testResult.success) {
      console.log(green(`✅ Connection successful (${testResult.latency}ms)`))

      // Try to fetch available models
      console.log(yellow('Fetching available models...'))
      const modelsResult = await getProviderModels(provider)

      if (modelsResult.success && modelsResult.models) {
        console.log(green(`✅ Found ${modelsResult.models.length} models`))
        if (modelsResult.models.length > 0) {
          console.log('Available models:')
          modelsResult.models.slice(0, 10).forEach(model => {
            const isDefault = model === provider.defaultModel
            console.log(`  ${isDefault ? green('●') : '○'} ${model}${isDefault ? green(' (default)') : ''}`)
          })
          if (modelsResult.models.length > 10) {
            console.log(`  ... and ${modelsResult.models.length - 10} more`)
          }
        }
      } else {
        console.log(yellow(`⚠️  Could not fetch models: ${modelsResult.error || 'Unknown error'}`))
      }
    } else {
      console.log(red(`❌ Connection failed: ${testResult.error}`))
      if (testResult.latency) {
        console.log(red(`   Response time: ${testResult.latency}ms`))
      }
    }
  } catch (error) {
    console.log(red(`❌ Test failed: ${error instanceof Error ? error.message : String(error)}`))
  }

  freshPrompt()
}

/**
 * Set provider for a specific agent
 */
async function handleSetAgentProvider(
  storage: ProviderStorage,
  providerId: string | undefined,
  agentId: string | undefined,
  freshPrompt: () => void
): Promise<void> {
  if (!providerId || !agentId) {
    console.log(red('Both provider ID and agent ID are required'))
    console.log('Usage: /provider set-agent <provider-id> <agent-id>')
    freshPrompt()
    return
  }

  const provider = storage.getProvider(providerId)
  if (!provider) {
    console.log(red(`Provider '${providerId}' not found`))
    freshPrompt()
    return
  }

  console.log(yellow('Note: Per-agent provider configuration is not yet fully implemented'))
  console.log(`This would set agent '${agentId}' to use provider '${providerId}'`)
  // TODO: Implement per-agent provider configuration in codebuff.json

  freshPrompt()
}

/**
 * Unset provider for a specific agent
 */
async function handleUnsetAgentProvider(
  storage: ProviderStorage,
  agentId: string | undefined,
  freshPrompt: () => void
): Promise<void> {
  if (!agentId) {
    console.log(red('Agent ID is required'))
    console.log('Usage: /provider unset-agent <agent-id>')
    freshPrompt()
    return
  }

  console.log(yellow('Note: Per-agent provider configuration is not yet fully implemented'))
  console.log(`This would remove provider override for agent '${agentId}'`)
  // TODO: Implement per-agent provider configuration removal

  freshPrompt()
}

/**
 * Show provider help
 */
export function showProviderHelp(): void {
  console.log(cyan('\n🔧 Provider Management Commands:'))
  console.log()
  console.log(`${bold('/provider add')}           - Add a new provider interactively`)
  console.log(`${bold('/provider list')}          - List all configured providers`)
  console.log(`${bold('/provider show <id>')}     - Show detailed provider information`)
  console.log(`${bold('/provider remove <id>')}   - Remove a provider`)
  console.log(`${bold('/provider set-global <id>')} - Set default provider`)
  console.log(`${bold('/provider set-agent <provider-id> <agent-id>')} - Set provider for specific agent`)
  console.log(`${bold('/provider unset-agent <agent-id>')} - Remove provider override for agent`)
  console.log(`${bold('/provider test <id>')}     - Test provider connection`)
  console.log()
  console.log('Examples:')
  console.log('  /provider add')
  console.log('  /provider list')
  console.log('  /provider show my-openai')
  console.log('  /provider set-global my-openai')
  console.log()
}

/**
 * Helper function to ask questions in readline
 */
function askQuestion(rl: readline.Interface, question: string, hidden = false): Promise<string> {
  return new Promise((resolve) => {
    if (hidden) {
      // For hidden input (like API keys), we'll just use regular input for now
      // In a real implementation, you might want to use a library like 'read' for hidden input
      console.log(yellow('(Input will be visible - consider using environment variables for sensitive data)'))
    }

    rl.question(question, (answer) => {
      resolve(answer.trim())
    })
  })
}
